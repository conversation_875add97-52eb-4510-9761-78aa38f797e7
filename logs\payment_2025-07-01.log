[2025-07-01 00:00:07] [INFO] Iniciando processamento de pagamento PIX
[2025-07-01 00:00:07] [INFO] Dados recebidos: {"product_id":1,"customer_name":"dfg","customer_email":"<EMAIL>","customer_whatsapp":"(43) 98857-4646","customer_document":"32210933811"}
[2025-07-01 00:00:07] [INFO] Conectando ao banco de dados
[2025-07-01 00:00:07] [INFO] Buscando produto ID: 1
[2025-07-01 00:00:07] [INFO] Produto encontrado: Curso de Programação Web - R$ 197.77
[2025-07-01 00:00:07] [ERROR] ERRO: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'phone' in 'field list'
[2025-07-01 00:00:34] [INFO] Iniciando processamento de pagamento PIX
[2025-07-01 00:00:34] [INFO] Dados recebidos: {"product_id":1,"customer_name":"dfg nn","customer_email":"<EMAIL>","customer_whatsapp":"(43) 98857-4646","customer_document":"32210933811"}
[2025-07-01 00:00:34] [INFO] Conectando ao banco de dados
[2025-07-01 00:00:34] [INFO] Buscando produto ID: 1
[2025-07-01 00:00:34] [INFO] Produto encontrado: Curso de Programação Web - R$ 197.77
[2025-07-01 00:00:34] [ERROR] ERRO: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'phone' in 'field list'
[2025-07-01 00:32:23] [INFO] Iniciando processamento de pagamento PIX
[2025-07-01 00:32:23] [INFO] Dados recebidos: {"product_id":1,"customer_name":"João Teste","customer_email":"<EMAIL>","customer_whatsapp":"11999999999","customer_document":"12345678901"}
[2025-07-01 00:32:23] [ERROR] ERRO: CPF/CNPJ válido é obrigatório
[2025-07-01 00:32:31] [INFO] Iniciando processamento de pagamento PIX
[2025-07-01 00:32:31] [INFO] Dados recebidos: {"product_id":1,"customer_name":"João Teste","customer_email":"<EMAIL>","customer_whatsapp":"11999999999","customer_document":"32210933811"}
[2025-07-01 00:32:31] [INFO] Conectando ao banco de dados
[2025-07-01 00:32:31] [INFO] Buscando produto ID: 1
[2025-07-01 00:32:31] [INFO] Produto encontrado: Curso de Programação Web - R$ 197.77
[2025-07-01 00:32:31] [INFO] Novo cliente criado: ID 6
[2025-07-01 00:32:31] [INFO] Criando pedido
[2025-07-01 00:32:31] [INFO] Pedido criado: ID 6
[2025-07-01 00:32:31] [INFO] Criando cliente no Asaas
[2025-07-01 00:32:32] [INFO] Cliente Asaas - Status: 400
[2025-07-01 00:32:32] [ERROR] ERRO: Erro ao criar cliente: O telefone informado é inválido.
[2025-07-01 00:32:49] [INFO] Iniciando processamento de pagamento PIX
[2025-07-01 00:32:49] [INFO] Dados recebidos: {"product_id":1,"customer_name":"João Teste","customer_email":"<EMAIL>","customer_whatsapp":"43988574646","customer_document":"12345678901"}
[2025-07-01 00:32:49] [ERROR] ERRO: CPF/CNPJ válido é obrigatório
[2025-07-01 00:33:02] [INFO] Iniciando processamento de pagamento PIX
[2025-07-01 00:33:02] [INFO] Dados recebidos: {"product_id":1,"customer_name":"João Teste","customer_email":"<EMAIL>","customer_whatsapp":"43988574313","customer_document":"32210933811"}
[2025-07-01 00:33:02] [INFO] Conectando ao banco de dados
[2025-07-01 00:33:02] [INFO] Buscando produto ID: 1
[2025-07-01 00:33:02] [INFO] Produto encontrado: Curso de Programação Web - R$ 197.77
[2025-07-01 00:33:02] [INFO] Cliente existente encontrado: ID 6
[2025-07-01 00:33:02] [INFO] Criando pedido
[2025-07-01 00:33:02] [INFO] Pedido criado: ID 7
[2025-07-01 00:33:02] [INFO] Criando cliente no Asaas
[2025-07-01 00:33:04] [INFO] Cliente Asaas - Status: 200
[2025-07-01 00:33:04] [INFO] Criando cobrança PIX no Asaas
[2025-07-01 00:33:05] [INFO] Cobrança PIX - Status: 200
[2025-07-01 00:33:05] [INFO] Obtendo QR Code PIX
[2025-07-01 00:33:07] [INFO] QR Code PIX - Status: 200, Response: {"success":true,"encodedImage":"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","payload":"00020101021226860014br.gov.bcb.pix2564qrpix.bradesco.com.br/qr/v2/84443499-5ec3-4fd6-89f8-0270cee33ec05204000053039865406197.775802BR5905ASAAS6009JOINVILLE62070503***6304EB8C","expirationDate":"2025-06-30 23:59:59"}
[2025-07-01 00:33:07] [INFO] Processamento concluído com sucesso - Order ID: 7, Payment ID: pay_0or75ybgdahadi2q
[2025-07-01 00:33:51] [INFO] Iniciando processamento de pagamento PIX
[2025-07-01 00:33:51] [INFO] Dados recebidos: {"product_id":2,"customer_name":"gabriela","customer_email":"<EMAIL>","customer_whatsapp":"(43) 98857-4316","customer_document":"32210933811"}
[2025-07-01 00:33:51] [INFO] Conectando ao banco de dados
[2025-07-01 00:33:51] [INFO] Buscando produto ID: 2
[2025-07-01 00:33:51] [INFO] Produto encontrado: E-book Marketing Digital - R$ 97.77
[2025-07-01 00:33:51] [INFO] Novo cliente criado: ID 9
[2025-07-01 00:33:51] [INFO] Criando pedido
[2025-07-01 00:33:51] [INFO] Pedido criado: ID 8
[2025-07-01 00:33:51] [INFO] Criando cliente no Asaas
[2025-07-01 00:33:54] [INFO] Cliente Asaas - Status: 200
[2025-07-01 00:33:54] [INFO] Criando cobrança PIX no Asaas
[2025-07-01 00:33:56] [INFO] Cobrança PIX - Status: 200
[2025-07-01 00:33:56] [INFO] Obtendo QR Code PIX
[2025-07-01 00:33:57] [INFO] QR Code PIX - Status: 200, Response: {"success":true,"encodedImage":"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","payload":"00020101021226860014br.gov.bcb.pix2564qrpix.bradesco.com.br/qr/v2/1519cf1a-348d-458f-8bd2-a5e3672bcfbb520400005303986540597.775802BR5905ASAAS6009JOINVILLE62070503***6304CF09","expirationDate":"2025-06-30 23:59:59"}
[2025-07-01 00:33:57] [INFO] Processamento concluído com sucesso - Order ID: 8, Payment ID: pay_ocyi65qjqcem58vo
[2025-07-01 04:17:14] [INFO] Iniciando processamento de pagamento PIX
[2025-07-01 04:17:14] [INFO] Dados recebidos: {"product_id":3,"customer_name":"ssdsd","customer_email":"<EMAIL>","customer_whatsapp":"(74) 54485-4545","customer_document":"32210933811"}
[2025-07-01 04:17:14] [INFO] Conectando ao banco de dados
[2025-07-01 04:17:14] [INFO] Buscando produto ID: 3
[2025-07-01 04:17:14] [INFO] Produto encontrado: Template WordPress Premium - R$ 147.77
[2025-07-01 04:17:14] [INFO] Novo cliente criado: ID 11
[2025-07-01 04:17:14] [INFO] Criando pedido
[2025-07-01 04:17:14] [INFO] Pedido criado: ID 9
[2025-07-01 04:17:14] [INFO] Criando cliente no Asaas
[2025-07-01 04:17:15] [INFO] Cliente Asaas - Status: 200
[2025-07-01 04:17:15] [INFO] Criando cobrança PIX no Asaas
[2025-07-01 04:17:16] [INFO] Cobrança PIX - Status: 200
[2025-07-01 04:17:16] [INFO] Obtendo QR Code PIX
[2025-07-01 04:17:16] [INFO] QR Code PIX - Status: 200, Response: {"success":true,"encodedImage":"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","payload":"00020101021226860014br.gov.bcb.pix2564qrpix.bradesco.com.br/qr/v2/eed9d3af-f745-41da-b3d2-60021b464afd5204000053039865406147.775802BR5905ASAAS6009JOINVILLE62070503***63042325","expirationDate":"2025-06-30 23:59:59"}
[2025-07-01 04:17:16] [INFO] Processamento concluído com sucesso - Order ID: 9, Payment ID: pay_qboznf2yo0rpib1d
