<?php
require_once 'session.php';
require_once 'database/connection.php';

// Inicializa variáveis de filtro
$search = $_GET['search'] ?? '';
$start_date = $_GET['start_date'] ?? '';
$end_date = $_GET['end_date'] ?? '';
$status = $_GET['status'] ?? '';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Construir a query base
    $query = "SELECT v.*, c.name as customer_name 
              FROM vendas v 
              LEFT JOIN customers c ON v.customer_id = c.id 
              WHERE 1=1";
    $params = [];
    
    // Adicionar filtros
    if ($search) {
        $query .= " AND (c.name LIKE ? OR v.id LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    if ($start_date) {
        $query .= " AND DATE(v.created_at) >= ?";
        $params[] = $start_date;
    }
    if ($end_date) {
        $query .= " AND DATE(v.created_at) <= ?";
        $params[] = $end_date;
    }
    if ($status) {
        $query .= " AND v.status = ?";
        $params[] = $status;
    }
    
    $query .= " ORDER BY v.created_at DESC";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $sales = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = 'Erro ao carregar vendas: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vendas - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        .status-badge {
            padding: 0.5em 1em;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 500;
        }
        .status-pending { background-color: #ffeeba; color: #856404; }
        .status-completed { background-color: #d4edda; color: #155724; }
        .status-cancelled { background-color: #f8d7da; color: #721c24; }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }
        .table th {
            border-top: none;
            background-color: #f8f9fa;
        }
        .search-box {
            border-radius: 20px;
            padding: 20px;
            background: #fff;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="d-flex" id="wrapper">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Page Content -->
        <div id="page-content-wrapper">
            <!-- Navbar -->
            <?php include 'includes/navbar.php'; ?>
            
            <div class="container-fluid px-4">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2 class="mb-0">
                                <i class="fas fa-shopping-cart me-2"></i>
                                Vendas
                            </h2>
                            <a href="new_sale.php" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>
                                Nova Venda
                            </a>
                        </div>
                        
                        <!-- Filtros -->
                        <div class="search-box mb-4">
                            <form method="GET" class="row g-3">
                                <div class="col-md-3">
                                    <input type="text" 
                                           class="form-control" 
                                           name="search" 
                                           value="<?php echo htmlspecialchars($search); ?>" 
                                           placeholder="Buscar por cliente ou ID">
                                </div>
                                <div class="col-md-2">
                                    <input type="date" 
                                           class="form-control" 
                                           name="start_date" 
                                           value="<?php echo $start_date; ?>">
                                </div>
                                <div class="col-md-2">
                                    <input type="date" 
                                           class="form-control" 
                                           name="end_date" 
                                           value="<?php echo $end_date; ?>">
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" name="status">
                                        <option value="">Todos os status</option>
                                        <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>Pendente</option>
                                        <option value="completed" <?php echo $status === 'completed' ? 'selected' : ''; ?>>Concluído</option>
                                        <option value="cancelled" <?php echo $status === 'cancelled' ? 'selected' : ''; ?>>Cancelado</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="fas fa-search me-2"></i>
                                        Filtrar
                                    </button>
                                    <a href="sales.php" class="btn btn-secondary">
                                        <i class="fas fa-redo me-2"></i>
                                        Limpar
                                    </a>
                                </div>
                            </form>
                        </div>
                        
                        <?php if (isset($error)): ?>
                        <div class="alert alert-danger">
                            <?php echo $error; ?>
                        </div>
                        <?php else: ?>
                        
                        <!-- Tabela de Vendas -->
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover table-sales">
                                        <thead>
                                            <tr>
                                                <th>#</th>
                                                <th>Cliente</th>
                                                <th>Data</th>
                                                <th>Valor</th>
                                                <th>Método</th>
                                                <th>Status</th>
                                                <th>Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($sales as $sale): ?>
                                            <tr>
                                                <td>#<?php echo str_pad($sale['id'], 5, '0', STR_PAD_LEFT); ?></td>
                                                <td><?php echo htmlspecialchars($sale['customer_name']); ?></td>
                                                <td><?php echo date('d/m/Y H:i', strtotime($sale['created_at'])); ?></td>
                                                <td>R$ <?php echo number_format($sale['total'], 2, ',', '.'); ?></td>
                                                <td><?php 
                                                    $payment_method = $sale['payment_method'] ?? 'N/A';
                                                    echo strtoupper($payment_method);
                                                ?></td>
                                                <td>
                                                    <span class="status-badge status-<?php echo $sale['payment_status']; ?>">
                                                        <?php
                                                        $status_labels = [
                                                            'pending' => 'Pendente',
                                                            'approved' => 'Aprovado',
                                                            'completed' => 'Concluído',
                                                            'cancelled' => 'Cancelado',
                                                            'refunded' => 'Reembolsado'
                                                        ];
                                                        echo $status_labels[$sale['payment_status']] ?? $sale['payment_status'];
                                                        ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="view_sale.php?id=<?php echo $sale['id']; ?>"
                                                       class="btn btn-sm btn-info btn-action me-1"
                                                       title="Ver detalhes">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if ($sale['status'] === 'pending'): ?>
                                                    <button type="button"
                                                            class="btn btn-sm btn-success btn-action me-1"
                                                            onclick="updateStatus(<?php echo $sale['id']; ?>, 'completed')"
                                                            title="Marcar como concluído">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button type="button"
                                                            class="btn btn-sm btn-danger btn-action"
                                                            onclick="updateStatus(<?php echo $sale['id']; ?>, 'cancelled')"
                                                            title="Cancelar venda">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                            
                                            <?php if (empty($sales)): ?>
                                            <tr>
                                                <td colspan="7" class="text-center py-4">
                                                    <i class="fas fa-inbox fa-2x mb-3 text-muted d-block"></i>
                                                    Nenhuma venda encontrada
                                                </td>
                                            </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/scripts.js"></script>
    <script>
    function updateStatus(id, status) {
        if (confirm('Tem certeza que deseja alterar o status desta venda?')) {
            fetch('api/update_sale_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: id,
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Erro ao atualizar status: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Erro ao atualizar status');
            });
        }
    }
    </script>
</body>
</html>
