<?php
require_once 'includes/auth_check.php';
require_once 'includes/header.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Get orders with product details
    $stmt = $pdo->query("
        SELECT o.*, p.name as product_name
        FROM orders o
        LEFT JOIN products p ON o.product_id = p.id
        ORDER BY o.created_at DESC
    ");
    $orders = $stmt->fetchAll();
} catch (Exception $e) {
    $error = $e->getMessage();
}
?>

<div class="container-fluid px-4">
    <h2 class="fs-2 mb-4">Pedidos</h2>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-sales">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Cliente</th>
                            <th>Produto</th>
                            <th>Valor</th>
                            <th>Status</th>
                            <th>Data</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($orders as $order): ?>
                            <tr>
                                <td><?php echo $order['id']; ?></td>
                                <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                                <td><?php echo htmlspecialchars($order['product_name']); ?></td>
                                <td>R$ <?php echo number_format($order['amount'], 2, ',', '.'); ?></td>
                                <td>
                                    <span class="badge bg-<?php
                                        echo $order['payment_status'] === 'paid' ? 'success' :
                                            ($order['payment_status'] === 'pending' ? 'warning' : 'danger');
                                    ?>">
                                        <?php
                                        $status_labels = [
                                            'pending' => 'Pendente',
                                            'paid' => 'Pago',
                                            'failed' => 'Falhou',
                                            'cancelled' => 'Cancelado'
                                        ];
                                        echo $status_labels[$order['payment_status']] ?? ucfirst($order['payment_status']);
                                        ?>
                                    </span>
                                </td>
                                <td><?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></td>
                                <td>
                                    <a href="view_order.php?id=<?php echo $order['id']; ?>"
                                       class="btn btn-sm btn-info btn-action">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
